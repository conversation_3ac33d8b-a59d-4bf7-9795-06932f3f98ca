{"info": {"name": "AIVIA VideoAgent API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Streamlined Postman collection for AIVIA Video Automation Backend with essential API endpoints for authentication, user configuration, accounts, video tasks, videos, and payments.", "version": "4.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "Base URL for the API"}, {"key": "token", "value": "", "description": "Authentication token - set after login"}, {"key": "active_video_id", "value": "1", "description": "Active video ID for testing - update with actual video ID"}, {"key": "manual_video_id", "value": "", "description": "Manual approval video ID - automatically set when creating manual approval videos"}, {"key": "draft_task_id", "value": "", "description": "Draft task ID - automatically set when saving drafts"}, {"key": "production_task_id", "value": "", "description": "Production task ID - automatically set when proceeding with tasks"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/register", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\",\n  \"password\": \"Pass@1234\",\n  \"confirm_password\": \"Pass@1234\",\n  \"mobile\": \"1234567890\"\n}"}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/verify-otp", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.token) {", "    pm.environment.set('token', json.token);", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/login", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Pass@1234\"\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "pm.environment.set('token', json.token);"]}}]}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/profile"}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/change-password", "body": {"mode": "raw", "raw": "{\n  \"old_password\": \"Pass@1234\",\n  \"new_password\": \"NewPass@1234\",\n  \"confirm_password\": \"NewPass@1234\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/logout"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/forgot-password", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/reset-password", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\",\n  \"new_password\": \"NewPass@1234\",\n  \"confirm_password\": \"NewPass@1234\"\n}"}}}]}, {"name": "User Configuration", "description": "User configuration management with JSON storage", "item": [{"name": "Get User Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "description": "Retrieve current user configuration. Returns 404 if no configuration exists."}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('User Config:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Save User Configuration (Create/Update)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"theme\": \"dark\",\n    \"language\": \"en\",\n    \"video_preferences\": {\n      \"default_orientation\": \"landscape\",\n      \"default_duration\": 60,\n      \"auto_publish\": false\n    },\n    \"notifications\": {\n      \"email\": true,\n      \"push\": false,\n      \"completion_alerts\": true\n    }\n  }\n}"}, "description": "Save or update user configuration. Merges with existing configuration if it exists."}}, {"name": "Update Specific Settings (Partial Update)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"theme\": \"light\",\n    \"notifications\": {\n      \"email\": false\n    }\n  }\n}"}, "description": "Partial update - only updates specified fields, keeps existing settings intact"}}, {"name": "Replace Configuration (Complete Replace)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "body": {"mode": "raw", "raw": "{\n  \"config\": {\n    \"version\": \"2.0\",\n    \"minimal_setup\": true,\n    \"features\": [\"user_config\", \"video_creation\"]\n  }\n}"}, "description": "Completely replaces existing configuration with new data"}}, {"name": "Delete User Configuration", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/config", "description": "Delete user configuration completely"}}]}, {"name": "Accounts", "item": [{"name": "List Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts"}}, {"name": "Create Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyC<PERSON><PERSON>\",\n  \"topic\": \"Tech Reviews\",\n  \"platforms\": [\"YouTube\",\"TikTok\"],\n  \"credentials\": {\"api_key\":\"abc123\"},\n  \"language\": \"en\",\n  \"status\": \"active\"\n}"}}}, {"name": "Get Account", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}, {"name": "Update Account", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyUpdatedChannel\"\n}"}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}]}, {"name": "Video Tasks", "item": [{"name": "List Video Tasks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks"}}, {"name": "Save Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an educational video about renewable energy sources like solar and wind power. Explain their benefits and environmental impact.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"alloy\",\n  \"video_style\": \"modern\",\n  \"bgm\": \"upbeat_corporate\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 60\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('draft_task_id', json.task_id);", "}"]}}]}, {"name": "Update Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"id\": {{draft_task_id}},\n  \"video_type\": \"avatar\",\n  \"context\": \"Updated: Create a comprehensive guide about artificial intelligence\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"rachel\",\n  \"video_style\": \"cinematic\",\n  \"bgm\": \"calm_piano\",\n  \"image_provider\": \"dalle\",\n  \"orientation\": \"portrait\",\n  \"duration\": 90\n}"}}}, {"name": "Save and Proceed Task (Start Video Creation)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an engaging video about the future of electric vehicles and sustainable transportation. Cover battery technology, charging infrastructure, and environmental benefits.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"nova\",\n  \"video_style\": \"dynamic\",\n  \"bgm\": \"electronic_beat\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 120,\n  \"auto_approval_each_stage\": true\n}"}, "description": "Create task and start video creation with automatic stage progression"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('production_task_id', json.task_id);", "}", "if (json.video_id) {", "    pm.environment.set('active_video_id', json.video_id);", "}"]}}]}, {"name": "Save and Proceed Task (Manual Approval)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create a professional video about AI in healthcare.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"rachel\",\n  \"video_style\": \"professional\",\n  \"bgm\": \"calm_corporate\",\n  \"image_provider\": \"dalle\",\n  \"orientation\": \"landscape\",\n  \"duration\": 90,\n  \"auto_approval_each_stage\": false\n}"}, "description": "Create task requiring manual approval at each stage"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('manual_task_id', json.task_id);", "}", "if (json.video_id) {", "    pm.environment.set('manual_video_id', json.video_id);", "}"]}}]}, {"name": "Get Video Task", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}, {"name": "Delete Video Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}]}, {"name": "Videos", "item": [{"name": "List Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos"}}, {"name": "Get Video Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}"}}, {"name": "Get Video Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/status", "description": "Get detailed video creation progress including stage, progress percentage, and estimated completion time"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('Video Status:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Update Video", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}", "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Video Title\",\n  \"description\": \"Updated video description with more details\"\n}"}}}]}, {"name": "Video Retry System", "description": "Stage-based video retry functionality", "item": [{"name": "Retry Script Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/script_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Script quality needs improvement - retry with enhanced prompts\"\n}"}, "description": "Retry the script generation stage. This is usually the first stage and has no dependencies."}}, {"name": "Retry Voice Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/voice_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Voice quality or pronunciation needs improvement\"\n}"}, "description": "Retry voice generation stage. Depends on script generation being completed."}}, {"name": "Retry Caption Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/caption_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Caption timing and accuracy needs adjustment\"\n}"}, "description": "Retry caption generation stage."}}, {"name": "Retry Image Prompt Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/image_prompt_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Image prompts need to be more specific and detailed\"\n}"}, "description": "Retry image prompt generation. Depends on script generation being completed."}}, {"name": "Retry Image Generation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/image_generation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Generated images don't match the content requirements\"\n}"}, "description": "Retry image generation stage."}}, {"name": "Retry Track Creation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/track_creation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Audio track configuration needs refinement\"\n}"}, "description": "Retry track creation stage."}}, {"name": "Retry Clip Creation Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/clip_creation", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Clip timing and transitions need adjustment\"\n}"}, "description": "Retry clip creation stage."}}, {"name": "Retry Video Composition Stage (Final)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/retry/video_composition", "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Final video composition needs quality improvements\"\n}"}, "description": "Retry final composition stage. Depends on all previous stages being completed."}}]}, {"name": "Manual Video Flow Control", "description": "Manual progression control for videos with auto_approval_each_stage disabled", "item": [{"name": "Proceed to Next Stage (Approve)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"feedback\": \"Stage looks good, approved for progression\",\n  \"reason\": \"Manual approval after review\"\n}"}, "description": "Approve current stage and proceed to next stage for videos with manual flow control"}}, {"name": "Reject Current Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": false,\n  \"feedback\": \"Quality needs improvement. Script requires more engaging content and better flow.\",\n  \"reason\": \"Content quality review - needs revision\"\n}"}, "description": "Reject current stage and require rework"}}, {"name": "Approve with Detailed Fe<PERSON>back", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"feedback\": \"Excellent work on this stage. The content is engaging and well-structured. Ready to proceed to next stage.\",\n  \"reason\": \"Quality approval after comprehensive review\"\n}"}, "description": "Approve stage with comprehensive feedback"}}, {"name": "Manual Video - Proceed to Next Stage", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{manual_video_id}}/proceed_to_next_stage", "body": {"mode": "raw", "raw": "{\n  \"approve\": true,\n  \"feedback\": \"Manual approval video stage approved\",\n  \"reason\": \"Manual review completed\"\n}"}, "description": "Proceed to next stage for manual approval video using manual_video_id variable"}}]}, {"name": "Video Creation Flow - Callbacks", "description": "Endpoints for N8N to send callbacks during video creation stages", "item": [{"name": "Script Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/script_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_script_001\",\n  \"script\": \"Welcome to our comprehensive guide on renewable energy...\",\n  \"title\": \"Renewable Energy: Powering the Future\",\n  \"description\": \"An in-depth look at renewable energy sources.\"\n}"}}}, {"name": "Voice Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/voice_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_voice_001\",\n  \"speech_url\": \"https://storage.example.com/audio/voice_{{active_video_id}}.mp3\"\n}"}}}, {"name": "Caption Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/caption_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_caption_001\",\n  \"caption_data\": {\n    \"subtitle_url\": \"https://storage.example.com/subtitles/video_{{active_video_id}}.srt\"\n  }\n}"}}}, {"name": "Image Prompt Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/image_prompt_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_image_prompt_001\",\n  \"image_prompts\": [\n    {\n      \"prompt\": \"Modern solar panel installation on residential rooftop\",\n      \"scene_timing\": {\"start\": 0, \"duration\": 5}\n    }\n  ]\n}"}}}, {"name": "Image Generation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/image_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_image_001\",\n  \"generated_images\": [\n    {\n      \"image_url\": \"https://storage.example.com/images/solar_panels_001.jpg\",\n      \"prompt_id\": 1\n    }\n  ]\n}"}}}, {"name": "Track Creation Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/track_creation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_track_001\",\n  \"tracks\": [\n    {\n      \"type\": \"video\",\n      \"layer\": 0\n    },\n    {\n      \"type\": \"audio\",\n      \"layer\": 0\n    }\n  ]\n}"}}}, {"name": "C<PERSON> <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/clip_creation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_clip_001\",\n  \"clips\": [\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 1,\n      \"start_time\": 0.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    }\n  ]\n}"}}}, {"name": "Video Composition Callback (Final)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/video_composition", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_compose_001\",\n  \"production_url\": \"https://cdn.example.com/videos/final_{{active_video_id}}.mp4\",\n  \"duration\": 120,\n  \"thumbnail_url\": \"https://cdn.example.com/thumbnails/video_{{active_video_id}}.jpg\"\n}"}}}, {"name": "Error Callback Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/voice_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"error\",\n  \"execution_id\": \"exec_voice_001\",\n  \"error_message\": \"TTS service temporarily unavailable. Please try again later.\"\n}"}}}]}, {"name": "Media Management", "item": [{"name": "List Tracks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tracks"}}, {"name": "List Media Generations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-generations"}}, {"name": "List Media Assets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-assets"}}, {"name": "List Clips", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/clips"}}]}, {"name": "Configurations", "item": [{"name": "Get Configuration Options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/configurations", "description": "Get all available options for video creation including TTS voices, video styles, orientations, etc."}}]}, {"name": "Payments", "item": [{"name": "List Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/packages"}}, {"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/create-checkout-session", "body": {"mode": "raw", "raw": "{\n  \"package_id\": 1,\n  \"success_url\": \"http://localhost:3000/success\",\n  \"cancel_url\": \"http://localhost:3000/cancel\"\n}"}}}, {"name": "List User Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/user-packages"}}, {"name": "List Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-history"}}]}]}