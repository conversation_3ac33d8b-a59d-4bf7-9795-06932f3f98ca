FROM python:3.12-slim

# Install netcat for healthchecks
RUN apt-get update && apt-get install -y netcat-openbsd && rm -rf /var/lib/apt/lists/*

# set workdir
WORKDIR /app

# install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt


# expose port & default command
EXPOSE 8000
CMD ["sh", "-c", "python manage.py init_kafka_topics --ignore-errors && python manage.py makemigrations && python manage.py migrate && python manage.py setup_retry_policies && python manage.py setup_initial_data && python manage.py runserver 0.0.0.0:8000"]