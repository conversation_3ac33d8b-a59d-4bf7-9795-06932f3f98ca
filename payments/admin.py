from django.contrib import admin
from .models import Package, UserPackage, PaymentHistory

class UserPackageInline(admin.TabularInline):
    model = UserPackage
    extra = 0
    readonly_fields = ('started_at', 'created_at')

class PaymentHistoryInline(admin.TabularInline):
    model = PaymentHistory
    extra = 0
    readonly_fields = ('created_at',)

class PackageAdmin(admin.ModelAdmin):
    list_display = ('name', 'price', 'billing_cycle', 'is_active', 'created_at')
    list_filter = ('billing_cycle', 'is_active')
    search_fields = ('name', 'description')
    date_hierarchy = 'created_at'
    inlines = [UserPackageInline]

class UserPackageAdmin(admin.ModelAdmin):
    list_display = ('user', 'package', 'status', 'is_trial', 'started_at', 'end_at')
    list_filter = ('status', 'is_trial')
    search_fields = ('user__email', 'package__name')
    date_hierarchy = 'created_at'
    inlines = [PaymentHistoryInline]

class PaymentHistoryAdmin(admin.ModelAdmin):
    list_display = ('user', 'package', 'amount', 'status', 'payment_method', 'created_at')
    list_filter = ('status', 'payment_method')
    search_fields = ('user__email', 'package__name', 'stripe_payment_intent_id')
    date_hierarchy = 'created_at'

admin.site.register(Package, PackageAdmin)
admin.site.register(UserPackage, UserPackageAdmin)
admin.site.register(PaymentHistory, PaymentHistoryAdmin)
