from rest_framework import serializers
from .models import Package, UserPackage, PaymentHistory

class PackageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Package
        fields = '__all__'
        read_only_fields = ('id', 'created_at')

class UserPackageSerializer(serializers.ModelSerializer):
    package_details = PackageSerializer(source='package', read_only=True)
    
    class Meta:
        model = UserPackage
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')

class PaymentHistorySerializer(serializers.ModelSerializer):
    package_name = serializers.CharField(source='package.name', read_only=True)
    
    class Meta:
        model = PaymentHistory
        fields = '__all__'
        read_only_fields = ('id', 'user', 'created_at')

class CreateCheckoutSessionSerializer(serializers.Serializer):
    package_id = serializers.IntegerField(required=True)
    success_url = serializers.URLField(required=True)
    cancel_url = serializers.URLField(required=True)
    
    def validate_package_id(self, value):
        try:
            package = Package.objects.get(id=value, is_active=True)
            return value
        except Package.DoesNotExist:
            raise serializers.ValidationError("Package does not exist or is not active")