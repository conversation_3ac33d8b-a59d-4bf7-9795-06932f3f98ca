# Generated by Django 5.2.1 on 2025-08-27 12:57

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('billing_cycle', models.CharField(choices=[('monthly', 'Monthly'), ('yearly', 'Yearly'), ('one_time', 'One Time')], max_length=20)),
                ('features', models.J<PERSON><PERSON>ield(default=dict)),
                ('stripe_price_id', models.CharField(blank=True, max_length=100, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'packages',
            },
        ),
        migrations.CreateModel(
            name='UserPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('active', 'Active'), ('cancelled', 'Cancelled'), ('expired', 'Expired')], default='active', max_length=20)),
                ('stripe_subscription_id', models.CharField(blank=True, max_length=100, null=True)),
                ('started_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('is_trial', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_packages', to='payments.package')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_packages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'user_packages',
            },
        ),
        migrations.CreateModel(
            name='PaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('refunded', 'Refunded')], max_length=20)),
                ('stripe_payment_intent_id', models.CharField(blank=True, max_length=100, null=True)),
                ('stripe_invoice_id', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_method', models.CharField(default='card', max_length=100)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_history', to='payments.package')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_history', to=settings.AUTH_USER_MODEL)),
                ('user_package', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_history', to='payments.userpackage')),
            ],
            options={
                'db_table': 'payment_history',
            },
        ),
    ]
