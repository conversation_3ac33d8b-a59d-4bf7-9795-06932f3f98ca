from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.conf import settings
from django.utils import timezone
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
import stripe
import json
from datetime import timed<PERSON><PERSON>

from .models import Package, UserPackage, PaymentHistory
from .serializers import (
    PackageSerializer, UserPackageSerializer, PaymentHistorySerializer,
    CreateCheckoutSessionSerializer
)
from authentication.models import User

# Initialize Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class PackageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing available packages.
    """
    queryset = Package.objects.filter(is_active=True)
    serializer_class = PackageSerializer
    permission_classes = [permissions.IsAuthenticated]


class UserPackageViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing user's subscribed packages.
    """
    serializer_class = UserPackageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Return packages for the current authenticated user.
        """
        return UserPackage.objects.filter(user=self.request.user).order_by('-created_at')


class PaymentHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing payment history.
    """
    serializer_class = PaymentHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Return payment history for the current authenticated user.
        """
        return PaymentHistory.objects.filter(user=self.request.user).order_by('-created_at')


class CreateCheckoutSessionView(APIView):
    """
    API endpoint for creating a Stripe checkout session.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = CreateCheckoutSessionSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        package_id = serializer.validated_data['package_id']
        success_url = serializer.validated_data['success_url']
        cancel_url = serializer.validated_data['cancel_url']
        
        # Get the package
        package = get_object_or_404(Package, id=package_id, is_active=True)
        
        # Get or create Stripe customer
        user = request.user
        if not user.stripe_customer_id:
            # Create a new Stripe customer
            customer = stripe.Customer.create(
                email=user.email,
                name=user.name,
                metadata={
                    'user_id': user.id
                }
            )
            user.stripe_customer_id = customer.id
            user.save()
        
        # Determine the payment mode (subscription or one-time)
        if package.billing_cycle in ['monthly', 'yearly']:
            mode = 'subscription'
            
            # Create or get Stripe price
            if not package.stripe_price_id:
                # Create a recurring price in Stripe
                stripe_product = stripe.Product.create(
                    name=package.name,
                    description=package.description,
                    metadata={
                        'package_id': package.id
                    }
                )
                
                # Determine the interval (month or year)
                interval = 'month' if package.billing_cycle == 'monthly' else 'year'
                
                stripe_price = stripe.Price.create(
                    product=stripe_product.id,
                    unit_amount=int(package.price * 100),  # Convert to cents
                    currency='usd',
                    recurring={
                        'interval': interval,
                    },
                    metadata={
                        'package_id': package.id
                    }
                )
                
                # Save the Stripe price ID to the package
                package.stripe_price_id = stripe_price.id
                package.save()
            
            price_id = package.stripe_price_id
            
            # Create Stripe Checkout Session for subscription
            session = stripe.checkout.Session.create(
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price': price_id,
                    'quantity': 1,
                }],
                mode=mode,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'package_id': package.id,
                    'user_id': user.id,
                }
            )
        else:
            # One-time payment
            mode = 'payment'
            
            # Create Stripe Checkout Session for one-time payment
            session = stripe.checkout.Session.create(
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {
                            'name': package.name,
                            'description': package.description,
                        },
                        'unit_amount': int(package.price * 100),  # Convert to cents
                    },
                    'quantity': 1,
                }],
                mode=mode,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'package_id': package.id,
                    'user_id': user.id,
                }
            )
        
        # Return the Checkout Session ID
        return Response({'session_id': session.id})


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def stripe_webhook(request):
    """
    Webhook for handling Stripe events.
    """
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError as e:
        # Invalid payload
        return HttpResponse(status=400)
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        return HttpResponse(status=400)
    
    # Handle the event
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        
        # Get metadata from the session
        metadata = session.get('metadata', {})
        package_id = metadata.get('package_id')
        user_id = metadata.get('user_id')
        
        if not package_id or not user_id:
            return HttpResponse(status=400)
        
        try:
            user = User.objects.get(id=user_id)
            package = Package.objects.get(id=package_id)
            
            # Create a user package
            if session.get('mode') == 'subscription':
                # Subscription payment
                subscription_id = session.get('subscription')
                
                # Get subscription details
                subscription = stripe.Subscription.retrieve(subscription_id)
                
                # Calculate end date based on billing cycle
                started_at = timezone.now()
                if package.billing_cycle == 'monthly':
                    end_at = started_at + timedelta(days=30)
                elif package.billing_cycle == 'yearly':
                    end_at = started_at + timedelta(days=365)
                else:
                    end_at = None
                
                # Create user package
                user_package = UserPackage.objects.create(
                    user=user,
                    package=package,
                    status='active',
                    stripe_subscription_id=subscription_id,
                    started_at=started_at,
                    end_at=end_at
                )
                
                # Create payment history
                PaymentHistory.objects.create(
                    user=user,
                    package=package,
                    user_package=user_package,
                    amount=package.price,
                    status='succeeded',
                    stripe_payment_intent_id=session.get('payment_intent'),
                    payment_method='card'
                )
            else:
                # One-time payment
                # Create user package
                user_package = UserPackage.objects.create(
                    user=user,
                    package=package,
                    status='active',
                    started_at=timezone.now(),
                    end_at=None  # One-time packages don't expire
                )
                
                # Create payment history
                PaymentHistory.objects.create(
                    user=user,
                    package=package,
                    user_package=user_package,
                    amount=package.price,
                    status='succeeded',
                    stripe_payment_intent_id=session.get('payment_intent'),
                    payment_method='card'
                )
        except (User.DoesNotExist, Package.DoesNotExist) as e:
            # Log the error
            print(f"Error processing checkout.session.completed: {str(e)}")
            return HttpResponse(status=400)
    
    elif event['type'] == 'invoice.paid':
        # Handle subscription renewal
        invoice = event['data']['object']
        subscription_id = invoice.get('subscription')
        
        if not subscription_id:
            return HttpResponse(status=400)
        
        try:
            # Find the user package with this subscription ID
            user_package = UserPackage.objects.get(stripe_subscription_id=subscription_id)
            
            # Update the end date based on billing cycle
            if user_package.package.billing_cycle == 'monthly':
                user_package.end_at = timezone.now() + timedelta(days=30)
            elif user_package.package.billing_cycle == 'yearly':
                user_package.end_at = timezone.now() + timedelta(days=365)
            
            user_package.status = 'active'
            user_package.save()
            
            # Create payment history
            PaymentHistory.objects.create(
                user=user_package.user,
                package=user_package.package,
                user_package=user_package,
                amount=user_package.package.price,
                status='succeeded',
                stripe_invoice_id=invoice.get('id'),
                payment_method='card'
            )
        except UserPackage.DoesNotExist:
            # Log the error
            print(f"Error processing invoice.paid: UserPackage with subscription_id {subscription_id} not found")
            return HttpResponse(status=400)
    
    elif event['type'] == 'customer.subscription.deleted':
        # Handle subscription cancellation
        subscription = event['data']['object']
        subscription_id = subscription.get('id')
        
        if not subscription_id:
            return HttpResponse(status=400)
        
        try:
            # Find the user package with this subscription ID
            user_package = UserPackage.objects.get(stripe_subscription_id=subscription_id)
            
            # Update the user package
            user_package.status = 'cancelled'
            user_package.cancelled_at = timezone.now()
            user_package.save()
        except UserPackage.DoesNotExist:
            # Log the error
            print(f"Error processing customer.subscription.deleted: UserPackage with subscription_id {subscription_id} not found")
            return HttpResponse(status=400)
    
    # Return a 200 response to acknowledge receipt of the event
    return HttpResponse(status=200)
