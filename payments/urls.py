from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import (
    PackageViewSet, UserPackageViewSet, PaymentHistoryViewSet,
    CreateCheckoutSessionView, stripe_webhook
)

router = DefaultRouter(trailing_slash=False)
router.register(r'packages', PackageViewSet, basename='package')
router.register(r'user-packages', UserPackageViewSet, basename='user-package')
router.register(r'payment-history', PaymentHistoryViewSet, basename='payment-history')

urlpatterns = [
    path('', include(router.urls)),
    path('create-checkout-session', CreateCheckoutSessionView.as_view(), name='create-checkout-session'),
    path('webhook', stripe_webhook, name='stripe-webhook'),
]